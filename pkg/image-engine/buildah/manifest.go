package buildah

import (
	"context"
	"errors"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/containers/buildah/util"
	"github.com/containers/common/libimage"
	"github.com/containers/common/libimage/manifests"
	cp "github.com/containers/image/v5/copy"
	"github.com/containers/image/v5/manifest"
	"github.com/containers/image/v5/transports"
	"github.com/containers/image/v5/transports/alltransports"
	"github.com/containers/image/v5/types"
	"github.com/containers/storage"
	"github.com/hashicorp/go-multierror"
	"github.com/opencontainers/go-digest"
	imgspecv1 "github.com/opencontainers/image-spec/specs-go/v1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"
)

func (e *Engine) LookupManifest(name string) (*libimage.ManifestList, error) {
	return e.libimgRuntime.LookupManifestList(name)
}

func (e *Engine) CreateManifest(name string, opts *image.ManifestCreateOpts) (string, error) {
	store := e.ImgStore()
	systemCxt := e.SystemContext()
	list := manifests.Create()

	names, err := util.ExpandNames([]string{name}, systemCxt, store)
	if err != nil {
		return "", fmt.Errorf("encountered while expanding image name %q: %w", name, err)
	}

	return list.SaveToImage(store, "", names, manifest.DockerV2ListMediaType)
}

func (e *Engine) DeleteManifests(names []string, opts *image.ManifestDeleteOpts) error {
	runtime := e.ImgRuntime()

	rmiReports, rmiErrors := runtime.RemoveImages(context.Background(), names, &libimage.RemoveImagesOptions{
		Filters:        []string{"readonly=false"},
		LookupManifest: true,
	})
	for _, r := range rmiReports {
		for _, u := range r.Untagged {
			klog.Infof("untagged: %s", u)
		}
	}
	for _, r := range rmiReports {
		if r.Removed {
			klog.Infof("%s", r.ID)
		}
	}

	var multiE *multierror.Error
	multiE = multierror.Append(multiE, rmiErrors...)

	return multiE.ErrorOrNil()
}

func (e *Engine) InspectManifest(name string, opts *image.ManifestInspectOpts) (*libimage.ManifestListData, error) {
	runtime := e.ImgRuntime()

	// attempt to resolve the manifest list locally.
	manifestList, err := runtime.LookupManifestList(name)
	if err != nil {
		return nil, err
	}

	return manifestList.Inspect()
}

func (e *Engine) PushManifest(name, destSpec string, opts *image.PushOpts) error {
	runtime := e.ImgRuntime()
	store := e.ImgStore()
	systemCxt := e.SystemContext()
	systemCxt.OCIInsecureSkipTLSVerify = opts.SkipTLSVerify
	systemCxt.DockerInsecureSkipTLSVerify = types.NewOptionalBool(opts.SkipTLSVerify)

	manifestList, err := runtime.LookupManifestList(name)
	if err != nil {
		return err
	}

	_, list, err := manifests.LoadFromImage(store, manifestList.ID())
	if err != nil {
		return err
	}

	dest, err := alltransports.ParseImageName(destSpec)
	if err != nil {
		destTransport := strings.Split(destSpec, ":")[0]
		if t := transports.Get(destTransport); t != nil {
			return err
		}

		if strings.Contains(destSpec, "://") {
			return err
		}

		destSpec = "docker://" + destSpec
		dest2, err2 := alltransports.ParseImageName(destSpec)
		if err2 != nil {
			return err
		}
		dest = dest2
		klog.V(5).Infof("Assuming docker:// as the transport method for DESTINATION: %s", destSpec)
	}

	var manifestType string
	if opts.Format != "" {
		switch opts.Format {
		case "oci":
			manifestType = imgspecv1.MediaTypeImageManifest
		case "v2s2", "docker":
			manifestType = manifest.DockerV2Schema2MediaType
		default:
			return fmt.Errorf("unknown format %q. Choose on of the supported formats: 'oci' or 'v2s2'", opts.Format)
		}
	}
	pushOptions := manifests.PushOptions{
		Store:              store,
		SystemContext:      systemCxt,
		ImageListSelection: cp.CopySystemImage,
		Instances:          nil,
		ManifestType:       manifestType,
	}
	if opts.All {
		pushOptions.ImageListSelection = cp.CopyAllImages
	}
	if !opts.Quiet {
		pushOptions.ReportWriter = os.Stderr
	}

	// 使用指数退避重试机制推送 manifest，默认重试3次
	err = wait.ExponentialBackoff(wait.Backoff{
		Duration: 1 * time.Second,    // 初始等待时间为1秒
		Factor:   1.5,                // 每次重试后，等待时间增加50%
		Jitter:   0.1,                // 添加10%的随机抖动
		Steps:    maxPullPushRetries, // 最多重试3次
		Cap:      5 * time.Second,    // 单次等待时间最长为5秒
	}, func() (bool, error) {
		_, _, pushErr := list.Push(getContext(), dest, pushOptions)
		if pushErr != nil {
			klog.Warningf("Failed, retrying in 2s ... (%d/%d)", maxPullPushRetries-retryCount+1, maxPullPushRetries)
			return false, nil // 返回 false 表示继续重试，nil 表示不是致命错误
		}
		return true, nil // 成功，停止重试
	})

	if err != nil {
		return fmt.Errorf("failed to push manifest after %d retries: %w", maxPullPushRetries, err)
	}

	if opts.Rm {
		_, err = store.DeleteImage(manifestList.ID(), true)
	}

	return err
}

// AddToManifest :
// for `manifestName`: if it is not exist,will create a new one. if not, it must be an existed manifest name.
// for `imageNameOrIDList`:
// if element is a single image just add it,
// if element is a manifest will add it’s s all instance no matter what platform it is.
func (e *Engine) AddToManifest(manifestName string, imageNameOrIDList []string, opts *image.ManifestAddOpts) error {
	var (
		runtime = e.ImgRuntime()
	)

	// check whether manifestName is already existed.
	manifestList, err := runtime.LookupManifestList(manifestName)
	if err == nil {
		return e.addToManifestList(manifestList, imageNameOrIDList, opts)
	}

	if !errors.Is(err, storage.ErrImageUnknown) {
		return err
	}

	klog.Infof("will create a new one manifest with name %s", manifestName)
	// if not exit,create a new one
	_, err = e.CreateManifest(manifestName, &image.ManifestCreateOpts{})
	if err != nil {
		return fmt.Errorf("failed to create a new one manifest with name %s :%v", manifestName, err)
	}
	manifestList, err = runtime.LookupManifestList(manifestName)
	if err != nil {
		return err
	}

	err = e.addToManifestList(manifestList, imageNameOrIDList, opts)
	if err != nil {
		delErr := e.DeleteManifests([]string{manifestName}, &image.ManifestDeleteOpts{})
		if delErr != nil {
			return fmt.Errorf("failed to delete %s : %v", manifestName, delErr)
		}
		return err
	}

	return nil
}

func (e *Engine) addToManifestList(manifestList *libimage.ManifestList, imageNameOrIDList []string, opts *image.ManifestAddOpts) error {
	var (
		imageIDToAdd []string
		err          error
		store        = e.ImgStore()
	)

	// determine all images
	for _, imageNameOrID := range imageNameOrIDList {
		ret, err := e.getImageIDList(imageNameOrID)
		if err != nil {
			return fmt.Errorf("failed to look up %s", imageNameOrID)
		}

		imageIDToAdd = append(imageIDToAdd, ret...)
	}

	_, list, err := manifests.LoadFromImage(store, manifestList.ID())
	if err != nil {
		return err
	}

	// add each to manifest list
	for _, imageID := range imageIDToAdd {
		err = e.addOneToManifestList(list, imageID, opts)
		if err != nil {
			return fmt.Errorf("failed to add new image %s to manifest :%v ", imageID, err)
		}
	}

	_, err = list.SaveToImage(store, manifestList.ID(), nil, "")

	return err
}

func (e *Engine) addOneToManifestList(list manifests.List, imageSpec string, opts *image.ManifestAddOpts) error {
	store := e.ImgStore()
	systemCxt := e.SystemContext()

	ref, err := alltransports.ParseImageName(imageSpec)
	if err != nil {
		if ref, err = alltransports.ParseImageName(util.DefaultTransport + imageSpec); err != nil {
			// check if the local image exists
			if ref, _, err = util.FindImage(store, "", systemCxt, imageSpec); err != nil {
				return err
			}
		}
	}

	digestID, err := list.Add(getContext(), systemCxt, ref, opts.All)
	if err != nil {
		var storeErr error
		// Retry without a custom system context.  A user may want to add
		// a custom platform (see #3511).
		if ref, _, storeErr = util.FindImage(store, "", nil, imageSpec); storeErr != nil {
			klog.Errorf("Error while trying to find image on local storage: %v", storeErr)
			return err
		}
		digestID, storeErr = list.Add(getContext(), systemCxt, ref, opts.All)
		if storeErr != nil {
			klog.Errorf("Error while trying to add on manifest list: %v", storeErr)
			return err
		}
	}

	if opts.Os != "" {
		if err = list.SetOS(digestID, opts.Os); err != nil {
			return err
		}
	}
	if opts.OsVersion != "" {
		if err = list.SetOSVersion(digestID, opts.OsVersion); err != nil {
			return err
		}
	}
	if len(opts.OsFeatures) != 0 {
		if err = list.SetOSFeatures(digestID, opts.OsFeatures); err != nil {
			return err
		}
	}
	if opts.Arch != "" {
		if err = list.SetArchitecture(digestID, opts.Arch); err != nil {
			return err
		}
	}
	if opts.Variant != "" {
		if err = list.SetVariant(digestID, opts.Variant); err != nil {
			return err
		}
	}

	if len(opts.Annotations) != 0 {
		annotations := make(map[string]string)
		for _, annotationSpec := range opts.Annotations {
			spec := strings.SplitN(annotationSpec, "=", 2)
			if len(spec) != 2 {
				return fmt.Errorf("no value given for annotation %q", spec[0])
			}
			annotations[spec[0]] = spec[1]
		}
		if err = list.SetAnnotations(&digestID, annotations); err != nil {
			return err
		}
	}

	klog.Infof("adding image %s successfully", imageSpec)

	return nil
}

// getImageId get imageID by name Or id,what ever it is an image or a manifest
// if it is image just return imageID
// if it is a manifest, return its included instance IDs.
func (e *Engine) getImageIDList(imageNameOrID string) ([]string, error) {
	// try to look up `imageNameOrID` as ManifestList
	store := e.ImgStore()
	img, _, err := e.ImgRuntime().LookupImage(imageNameOrID, &libimage.LookupImageOptions{
		ManifestList: true,
	})
	if err != nil {
		return nil, err
	}

	isManifest, err := img.IsManifestList(getContext())
	if err != nil {
		return nil, err
	}

	// if not manifest, just return its ID.
	if !isManifest {
		return []string{img.ID()}, nil
	}

	// if it is a manifest, return its included instance ID.
	klog.Infof("image %q is a manifest list, looking up matching instances", imageNameOrID)

	imageName := img.Names()[0]
	manifestList, err := e.ImgRuntime().LookupManifestList(imageName)
	if err != nil {
		return nil, err
	}

	_, list, err := manifests.LoadFromImage(store, manifestList.ID())
	if err != nil {
		return nil, err
	}

	var imageIDList []string
	for _, instanceDigest := range list.Instances() {
		images, err := store.ImagesByDigest(instanceDigest)
		if err != nil {
			return nil, err
		}
		if len(images) == 0 {
			return nil, fmt.Errorf("no image matched with digest %s", instanceDigest)
		}
		imageIDList = append(imageIDList, images[0].ID)
	}

	return imageIDList, nil
}

func (e *Engine) RemoveFromManifest(name string, instanceDigest digest.Digest, opts *image.ManifestRemoveOpts) error {
	runtime := e.ImgRuntime()

	manifestList, err := runtime.LookupManifestList(name)
	if err != nil {
		return err
	}

	if err = manifestList.RemoveInstance(instanceDigest); err != nil {
		return err
	}

	klog.V(5).Infof("%s: %s", manifestList.ID(), instanceDigest.String())

	return nil
}
